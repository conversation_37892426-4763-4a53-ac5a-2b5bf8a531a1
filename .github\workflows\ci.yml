name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest

    env:
      # 禁用不必要的GitHub API调用
      ELECTRON_SKIP_BINARY_DOWNLOAD: 1
      ELECTRON_CACHE: .cache/electron

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Build Vue application
      run: npm run build
      
    - name: Check if build artifacts exist
      run: |
        if [ ! -d "dist" ]; then
          echo "Build failed: dist directory not found"
          exit 1
        fi
        echo "Build successful: dist directory created"

  build:
    needs: test
    strategy:
      fail-fast: false  # 不要因为一个平台失败就停止其他平台
      matrix:
        include:
          - os: ubuntu-latest
            platform: linux
            build-cmd: npm run dist -- --linux
          - os: windows-latest
            platform: windows
            build-cmd: npm run dist -- --win
          - os: macos-latest
            platform: macos
            build-cmd: npm run dist -- --mac

    runs-on: ${{ matrix.os }}

    env:
      # 禁用不必要的GitHub API调用和更新检查
      ELECTRON_SKIP_BINARY_DOWNLOAD: 1
      ELECTRON_CACHE: .cache/electron
      NO_UPDATE_NOTIFIER: 1
      DISABLE_OPENCOLLECTIVE: 1

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Build Vue application
      run: npm run build

    - name: Build Electron app (${{ matrix.platform }})
      run: ${{ matrix.build-cmd }}
      continue-on-error: false

    - name: Upload build artifacts
      uses: actions/upload-artifact@v4
      if: always()  # 即使构建失败也尝试上传已有的文件
      with:
        name: build-${{ matrix.platform }}
        path: dist/
        retention-days: 7
