name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Build Vue application
      run: npm run build
      
    - name: Check if build artifacts exist
      run: |
        if [ ! -d "dist" ]; then
          echo "Build failed: dist directory not found"
          exit 1
        fi
        echo "Build successful: dist directory created"

  build:
    needs: test
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        
    runs-on: ${{ matrix.os }}
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Build Vue application
      run: npm run build
      
    - name: Build Electron app (Linux)
      if: matrix.os == 'ubuntu-latest'
      run: npm run dist -- --linux
      
    - name: Build Electron app (Windows)
      if: matrix.os == 'windows-latest'
      run: npm run dist -- --win
      
    - name: Build Electron app (macOS)
      if: matrix.os == 'macos-latest'
      run: npm run dist -- --mac
      
    - name: Upload build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: build-${{ matrix.os }}
        path: dist/
        retention-days: 7
