{"name": "novelbox", "version": "0.7.1", "description": "AI辅助的小说创作工具", "main": "main.js", "scripts": {"start": "npm run build && electron .", "dev": "concurrently \"npm run dev-vue\" \"wait-on http://localhost:5173 && npm run dev-electron\"", "dev-vue": "vite", "dev-electron": "cross-env NODE_ENV=development electron .", "build": "vite build", "build-vue": "vite build", "preview": "vite preview", "test": "echo \"Error: no test specified\" && exit 1", "version-patch": "npm version patch && git push && git push --tags", "version-minor": "npm version minor && git push && git push --tags", "version-major": "npm version major && git push && git push --tags", "pack": "electron-builder --dir", "dist": "electron-builder"}, "keywords": ["electron", "vue", "novel", "writing", "ai"], "author": {"name": "NovelBox Team", "email": "<EMAIL>"}, "license": "AGPL-3.0", "type": "commonjs", "devDependencies": {"@vitejs/plugin-vue": "^5.2.4", "concurrently": "^9.2.0", "cross-env": "^7.0.3", "electron": "^37.2.4", "vite": "^6.3.5", "wait-on": "^8.0.4", "electron-builder": "^25.1.8"}, "dependencies": {"pinia": "^3.0.3", "vue": "^3.5.18", "vue-i18n": "^12.0.0-alpha.3", "vue-router": "^4.5.1"}, "build": {"extends": "./electron-builder.config.js"}}